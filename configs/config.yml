# configs/config.yml
app:
  name: final-ddd
  port: 8080  # 改为 8080 以匹配 Dockerfile
  env: ${APP_ENV:release}

database:
  type: ${DB_TYPE:postgres}
  user: ${DB_USER:postgres}
  password: ${DB_PASSWORD:password}
  host: ${DATABASE_HOST:postgres}
  port: ${DATABASE_PORT:5432}
  name: ${DB_NAME:final_ddd}
  path: ./sqlite_data

jwt:
  key: ${JWT_SECRET:default_secret_key}

redis:
  host: ${REDIS_HOST:redis}
  port: ${REDIS_PORT:6379}
  password: ${REDIS_PASSWORD:}
  db: 0

log:
  level: ${LOG_LEVEL:info}
  format: json
