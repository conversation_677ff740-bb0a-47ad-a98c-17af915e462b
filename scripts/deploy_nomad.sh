#!/bin/bash

set -e

# 显示部署信息
echo "开始部署一体化应用到 Nomad 集群..."

# 确保在项目根目录
cd "$(dirname "$0")/.."
PROJECT_ROOT=$(pwd)

# 解析命令行参数
ENVIRONMENT="dev"
TAG="latest"
DOMAIN_NAME="app.example.com"
NOMAD_ADDR="http://localhost:4646"

# 处理命令行参数
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -e|--env) ENVIRONMENT="$2"; shift ;;
    -t|--tag) TAG="$2"; shift ;;
    -d|--domain) DOMAIN_NAME="$2"; shift ;;
    -n|--nomad) NOMAD_ADDR="$2"; shift ;;
    *) echo "未知参数: $1"; exit 1 ;;
  esac
  shift
done

# 验证环境
if [[ ! "$ENVIRONMENT" =~ ^(dev|test|staging|prod)$ ]]; then
  echo "错误: 环境必须是 dev, test, staging 或 prod 之一"
  exit 1
fi

echo "部署环境: $ENVIRONMENT"
echo "镜像标签: $TAG"
echo "域名: $DOMAIN_NAME"
echo "Nomad 地址: $NOMAD_ADDR"

# 获取版本信息
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S')
COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建 Docker 镜像
echo "构建一体化应用 Docker 镜像..."
docker build \
  --build-arg VERSION="$VERSION" \
  --build-arg BUILD_TIME="$BUILD_TIME" \
  --build-arg COMMIT_HASH="$COMMIT_HASH" \
  -t "final-ddd:$TAG" \
  -t "final-ddd:$ENVIRONMENT" \
  .

# 标记镜像为本地 registry 格式（假设 registry 运行在 localhost:5000）
REGISTRY_URL="localhost:5000"
REGISTRY_IMAGE="$REGISTRY_URL/final-ddd:$TAG"
docker tag "final-ddd:$TAG" "$REGISTRY_IMAGE"

# 推送镜像到本地 registry
echo "推送镜像到本地 registry..."
docker push "$REGISTRY_IMAGE"

# 修改 Nomad 作业定义
echo "准备 Nomad 作业定义..."
cp "$PROJECT_ROOT/deployments/nomad/app-unified.nomad" "$PROJECT_ROOT/deployments/nomad/app-unified.nomad.tmp"

# 替换变量
sed -i "s|\${APP_IMAGE}|$REGISTRY_IMAGE|g" "$PROJECT_ROOT/deployments/nomad/app-unified.nomad.tmp"
sed -i "s|\${DOMAIN_NAME}|$DOMAIN_NAME|g" "$PROJECT_ROOT/deployments/nomad/app-unified.nomad.tmp"
sed -i "s|\${DATABASE_SERVICE}|postgres|g" "$PROJECT_ROOT/deployments/nomad/app-unified.nomad.tmp"
sed -i "s|\${ENVIRONMENT}|$ENVIRONMENT|g" "$PROJECT_ROOT/deployments/nomad/app-unified.nomad.tmp"

# 设置 Nomad 地址
export NOMAD_ADDR="$NOMAD_ADDR"

# 部署应用
echo "部署应用到 Nomad..."
nomad job run "$PROJECT_ROOT/deployments/nomad/app-unified.nomad.tmp"

# 清理临时文件
rm "$PROJECT_ROOT/deployments/nomad/app-unified.nomad.tmp"

echo "一体化应用已成功部署到 Nomad 集群！"
echo "访问地址: https://$DOMAIN_NAME" 