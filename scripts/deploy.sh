#!/bin/bash

set -e

# 显示部署信息
echo "开始部署一体化应用..."

# 确保在项目根目录
cd "$(dirname "$0")/.."
PROJECT_ROOT=$(pwd)

# 解析命令行参数
ENVIRONMENT="dev"
TAG="latest"

# 处理命令行参数
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -e|--env) ENVIRONMENT="$2"; shift ;;
    -t|--tag) TAG="$2"; shift ;;
    *) echo "未知参数: $1"; exit 1 ;;
  esac
  shift
done

# 验证环境
if [[ ! "$ENVIRONMENT" =~ ^(dev|test|staging|prod)$ ]]; then
  echo "错误: 环境必须是 dev, test, staging 或 prod 之一"
  exit 1
fi

echo "部署环境: $ENVIRONMENT"
echo "镜像标签: $TAG"

# 获取版本信息
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S')
COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建 Docker 镜像
echo "构建 Docker 镜像..."
docker build \
  --build-arg VERSION="$VERSION" \
  --build-arg BUILD_TIME="$BUILD_TIME" \
  --build-arg COMMIT_HASH="$COMMIT_HASH" \
  -t "final-ddd:$TAG" \
  -t "final-ddd:$ENVIRONMENT" \
  .

# 根据环境执行不同的部署操作
case "$ENVIRONMENT" in
  dev)
    echo "在开发环境中运行容器..."
    docker stop final-ddd-dev 2>/dev/null || true
    docker rm final-ddd-dev 2>/dev/null || true
    docker run -d --name final-ddd-dev \
      -p 8080:8080 \
      -v "$PROJECT_ROOT/configs:/app/configs" \
      -v "$PROJECT_ROOT/logs:/app/logs" \
      --restart unless-stopped \
      "final-ddd:$TAG"
    ;;
  test)
    echo "在测试环境中部署..."
    # 这里可以添加测试环境的部署逻辑，例如推送到测试服务器或 Kubernetes 集群
    ;;
  staging)
    echo "在预发布环境中部署..."
    # 这里可以添加预发布环境的部署逻辑
    ;;
  prod)
    echo "在生产环境中部署..."
    # 这里可以添加生产环境的部署逻辑，例如推送到 Docker 仓库，然后通过 CI/CD 部署
    ;;
esac

echo "部署完成！" 