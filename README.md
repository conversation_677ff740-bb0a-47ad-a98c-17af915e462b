# Final-DDD

基于领域驱动设计（DDD）的全栈应用程序，前后端集成在一个二进制文件中。

## 项目特点

- 基于 Go 语言和 Gin 框架的后端 API
- 前端静态文件嵌入到 Go 二进制文件中
- 领域驱动设计（DDD）架构
- 支持多种数据库（MySQL, PostgreSQL, SQLite）
- Redis 缓存支持
- JWT 认证
- Prometheus 监控
- Docker 容器化部署
- Nomad 集群部署支持

## 项目结构

```
.
├── cmd/                # 应用程序入口
├── configs/            # 配置文件
├── deployments/        # 部署相关配置
├── frontend/           # 前端应用程序
├── internal/           # 内部包
│   ├── application/    # 应用层
│   ├── domain/         # 领域层
│   ├── infrastructure/ # 基础设施层
│   └── interfaces/     # 接口层
├── pkg/                # 公共包
├── scripts/            # 构建和部署脚本
└── terraform/          # 基础设施即代码
```

## 开发环境设置

### 前提条件

- Go 1.23+
- Node.js 18+
- Docker (可选)
- Redis (可选，用于缓存)
- 数据库 (MySQL, PostgreSQL 或 SQLite)

### 本地开发

1. 克隆仓库

```bash
git clone https://github.com/azel-ko/final-ddd.git
cd final-ddd
```

2. 安装依赖

```bash
# 后端依赖
go mod download

# 前端依赖
cd frontend
npm install
cd ..
```

3. 构建前端

```bash
cd frontend
npm run build
cd ..
```

4. 复制前端构建文件到嵌入目录

```bash
mkdir -p internal/interfaces/http/router/frontend/dist
cp -r frontend/dist/* internal/interfaces/http/router/frontend/dist/
```

5. 运行应用程序

```bash
go run cmd/main.go
```

### 使用构建脚本

项目提供了一个自动化构建脚本，可以一键构建前后端：

```bash
./scripts/build.sh
```

构建完成后，可以直接运行生成的二进制文件：

```bash
./final-ddd
```

## 部署

### 使用 Docker

1. 构建 Docker 镜像

```bash
docker build -t final-ddd .
```

2. 运行 Docker 容器

```bash
docker run -d -p 8080:8080 --name final-ddd final-ddd
```

### 使用部署脚本

项目提供了一个部署脚本，可以在不同环境中部署应用程序：

```bash
# 部署到开发环境
./scripts/deploy.sh -e dev

# 部署到测试环境
./scripts/deploy.sh -e test

# 部署到预发布环境
./scripts/deploy.sh -e staging

# 部署到生产环境
./scripts/deploy.sh -e prod -t v1.0.0
```

### 使用 Nomad 部署

项目支持使用 HashiCorp Nomad 进行部署，特别适合大规模集群环境：

```bash
# 部署到 Nomad 开发环境
./scripts/deploy_nomad.sh -e dev -d dev.example.com

# 部署到 Nomad 测试环境
./scripts/deploy_nomad.sh -e test -d test.example.com -n http://nomad-server:4646

# 部署到 Nomad 预发布环境
./scripts/deploy_nomad.sh -e staging -d staging.example.com -n http://nomad-server:4646

# 部署到 Nomad 生产环境
./scripts/deploy_nomad.sh -e prod -d example.com -t v1.0.0 -n http://nomad-server:4646
```

#### Nomad 部署参数

- `-e, --env`: 部署环境 (dev, test, staging, prod)
- `-t, --tag`: 镜像标签
- `-d, --domain`: 应用程序域名
- `-n, --nomad`: Nomad 服务器地址

### CI/CD

项目使用 GitHub Actions 进行持续集成和部署：

- 推送到 `develop` 分支会自动部署到开发环境
- 推送到 `main` 或 `master` 分支会自动部署到预发布环境
- 创建以 `v` 开头的标签会自动部署到生产环境

## 配置

应用程序使用 YAML 配置文件，位于 `configs` 目录中。可以通过环境变量覆盖配置项。

## 许可证

MIT
