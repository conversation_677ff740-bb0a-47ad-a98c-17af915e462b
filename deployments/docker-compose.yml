services:
  traefik:
    image: docker.1ms.run/traefik:v2.10
    container_name: traefik
    ports:
      - "80:80"     # HTTP
      - "443:443"   # HTTPS
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ../configs/traefik/acme.json:/etc/traefik/acme.json
      - ../configs/traefik/certs:/etc/traefik/certs
      - traefik_logs:/var/log/traefik
    networks:
      - app_network
    command:
      - "--api=true"
      - "--api.dashboard=true"
      - "--api.insecure=true"
      - "--ping=true"
      - "--entryPoints.web.address=:80"
      - "--entryPoints.web.http.redirections.entryPoint.to=websecure"
      - "--entryPoints.web.http.redirections.entryPoint.scheme=https"
      - "--entryPoints.websecure.address=:443"
      - "--providers.docker=true"
      - "--providers.docker.endpoint=unix:///var/run/docker.sock"
      - "--providers.docker.exposedbydefault=false"
      - "--certificatesresolvers.leresolver.acme.tlschallenge=true"
      - "--certificatesresolvers.leresolver.acme.email=<EMAIL>"
      - "--certificatesresolvers.leresolver.acme.storage=acme.json"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik-dashboard.entrypoints=websecure"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      - "traefik.http.routers.traefik-dashboard.rule=PathPrefix(`/dash`)"
      - "traefik.http.middlewares.traefik-dashboard_strip.stripprefix.prefixes=/dash"
      - "traefik.http.routers.traefik-dashboard.middlewares=traefik-dashboard_strip"
      - "traefik.http.routers.traefik-dashboard.tls.certresolver=leresolver"

      - "traefik.http.routers.traefik-dashboard-api.entrypoints=websecure"
      - "traefik.http.routers.traefik-dashboard-api.service=api@internal"
      - "traefik.http.routers.traefik-dashboard-api.rule=PathPrefix(`/api`)"
      - "traefik.http.routers.traefik-dashboard-api.tls.certresolver=leresolver"
    restart: unless-stopped

  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: frontend
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
      - "traefik.http.routers.frontend.rule=HostRegexp(`{host:.+}`)"
      - "traefik.http.routers.frontend.priority=1"
      - "traefik.http.routers.frontend.tls.certresolver=leresolver"
    networks:
      - app_network
    depends_on:
      - app
    restart: unless-stopped


  mysql:
    image: docker.1ms.run/mysql:8.0
    container_name: mysql
    profiles:
      - mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-password}
      MYSQL_DATABASE: ${DB_NAME:-app}
      MYSQL_USER: user
      MYSQL_PASSWORD: ${DB_PASSWORD:-password}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ../configs/mysql/my.cnf:/etc/mysql/my.cnf:ro
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u$$MYSQL_USER", "-p$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  postgres:
    image: docker.1ms.run/postgres:14
    container_name: postgres
    profiles:
      - postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-app}
      POSTGRES_USER: ${DB_USER:-user}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  sqlite:
    image: docker.1ms.run/alpine:latest
    container_name: sqlite
    profiles:
      - sqlite
    volumes:
      - sqlite_data:/data
      - ${DB_PATH:-./sqlite_data}:/data/external
    command: |
      sh -c "
        apk add --no-cache sqlite
        chown -R 1000:1000 /data
        tail -f /dev/null
      "
    networks:
      - app_network
    restart: unless-stopped

  redis:
    image: docker.1ms.run/redis:7.0
    container_name: redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-password}
    volumes:
      - redis_data:/data
    networks:
      - app_network
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  rabbitmq:
    image: docker.1ms.run/rabbitmq:4-management
    container_name: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-password}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - app_network
    ports:
      - "15672:15672"  # Management UI
      - "5672:5672"    # AMQP protocol (for messaging)
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_port_connectivity"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  prometheus:
    image: docker.1ms.run/prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ../configs/prometheus/:/etc/prometheus/
      - prometheus_data:/prometheus
    networks:
      - app_network
    ports:
     - "9090:9090"  # Management UI
    restart: unless-stopped

  grafana:
    image: docker.1ms.run/grafana/grafana:latest
    container_name: grafana
    volumes:
      - grafana_data:/var/lib/grafana
      - ../configs/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_SERVER_ROOT_URL=https://grafana.azel.icu
    networks:
      - app_network
    ports:
      - "3000:3000"  # Management UI

    depends_on:
      - prometheus
    restart: unless-stopped

  app:
    build:
      context: ..
      dockerfile: Dockerfile
    image: go-app:latest
    container_name: app
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.entrypoints=websecure"
      - "traefik.http.services.app.loadbalancer.server.port=9999"
      - "traefik.http.routers.app.rule=PathPrefix(`/app`)"
      # 只去掉 /app 前缀,保留 /api
      - "traefik.http.middlewares.app-strip.stripprefix.prefixes=/app"
      - "traefik.http.routers.app.middlewares=app-strip@docker"
      - "traefik.http.routers.app.priority=2"
      - "traefik.http.routers.app.tls.certresolver=leresolver"
    networks:
      - app_network
    volumes:
      - app_log_data:/app/logs
    depends_on:
      - ${DATABASE_SERVICE:-mysql}  # 确保 DATABASE_SERVICE 环境变量正确设置
      - redis
      - rabbitmq
    restart: unless-stopped

networks:
  app_network:
    driver: bridge

volumes:
  mysql_data:
  postgres_data:
  sqlite_data:
  redis_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:
  traefik_logs:
  app_log_data:
