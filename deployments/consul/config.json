{"datacenter": "dc1", "data_dir": "/opt/consul", "log_level": "INFO", "node_name": "consul-server", "server": true, "bootstrap_expect": 1, "ui_config": {"enabled": true}, "client_addr": "0.0.0.0", "bind_addr": "0.0.0.0", "advertise_addr": "{{ GetInterfaceIP \"eth0\" }}", "ports": {"http": 8500, "https": -1, "grpc": 8502}, "connect": {"enabled": true}, "acl": {"enabled": false, "default_policy": "allow", "enable_token_persistence": true}}