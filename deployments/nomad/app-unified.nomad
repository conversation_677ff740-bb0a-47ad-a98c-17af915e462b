job "app-unified" {
  datacenters = ["dc1"]
  type = "service"

  group "app" {
    count = 1

    host_volume "app-logs" {
      path      = "/opt/data/app"
      read_only = false
    }

    network {
      port "http" {
        to = 8080
      }
    }

    service {
      name = "app-unified"
      port = "http"

      # 添加域名相关的标签
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.app.entrypoints=websecure",
        "traefik.http.routers.app.rule=Host(`${DOMAIN_NAME}`)",
        "traefik.http.routers.app.priority=10",
        "traefik.http.routers.app.tls.certresolver=leresolver"
      ]

      check {
        type     = "http"
        path     = "/api/health"
        interval = "10s"
        timeout  = "2s"
      }
    }

    task "app-unified" {
      driver = "docker"

      config {
        image = "${APP_IMAGE}"
        ports = ["http"]
        volumes = [
          "/opt/data/app:/app/logs"
        ]
        # 告诉 Nomad 不要从远程仓库拉取镜像
        force_pull = false
      }

      # 使用模板获取服务地址
      template {
        data = <<EOF
# 服务发现配置
POSTGRES_ADDR={{ range service "postgres" }}{{ .Address }}:{{ .Port }}{{ end }}
REDIS_ADDR={{ range service "redis" }}{{ .Address }}:{{ .Port }}{{ end }}
RABBITMQ_ADDR={{ range service "rabbitmq" }}{{ .Address }}:{{ .Port }}{{ end }}
EOF
        destination = "local/service-discovery.env"
        env = true
      }

      env {
        # 环境变量配置，根据实际需要设置
        DATABASE_SERVICE = "${DATABASE_SERVICE}"
        DOMAIN_NAME = "${DOMAIN_NAME}"
        APP_ENV = "${ENVIRONMENT}"
      }

      resources {
        cpu    = 800
        memory = 768
      }

      logs {
        max_files     = 10
        max_file_size = 10
      }
    }

    ephemeral_disk {
      size = 300
    }
  }
} 