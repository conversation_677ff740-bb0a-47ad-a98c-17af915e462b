# 生产环境配置

# 基础配置
APP_ENV=prod
DOMAIN_NAME=your-domain.com
ACME_EMAIL=<EMAIL>

# Nomad/Consul 配置
NOMAD_ADDR=http://nomad-server:4646
CONSUL_ADDR=http://consul-server:8500

# 数据库配置
DB_NAME=final_ddd
DB_USER=postgres
DB_PASSWORD=secure_production_password

# 应用配置
JWT_SECRET=secure_production_jwt_secret_key
LOG_LEVEL=info

# 集群配置
POSTGRES_NODE_CLASS=database
REGISTRY_NODE_CLASS=storage

# Registry 配置
USE_LOCAL_REGISTRY=true
LOCAL_REGISTRY_ADDR=registry.your-domain.com:5000

# 镜像配置
APP_IMAGE=registry.your-domain.com:5000/final-ddd:latest
