# 测试环境配置

# 基础配置
APP_ENV=staging
DOMAIN_NAME=staging.your-domain.com
ACME_EMAIL=<EMAIL>

# Nomad/Consul 配置
NOMAD_ADDR=http://nomad-staging:4646
CONSUL_ADDR=http://consul-staging:8500

# 数据库配置
DB_NAME=final_ddd_staging
DB_USER=postgres
DB_PASSWORD=staging_password

# 应用配置
JWT_SECRET=staging_jwt_secret_key
LOG_LEVEL=info

# Registry 配置
REGISTRY_NODE=staging-registry
USE_LOCAL_REGISTRY=true
LOCAL_REGISTRY_ADDR=registry.staging.your-domain.com:5000

# 镜像配置
APP_IMAGE=registry.staging.your-domain.com:5000/final-ddd:latest
