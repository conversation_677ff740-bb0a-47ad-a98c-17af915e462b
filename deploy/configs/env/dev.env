# 开发环境配置

# 基础配置
APP_ENV=dev
DOMAIN_NAME=app.local
ACME_EMAIL=<EMAIL>

# Nomad/Consul 配置
NOMAD_ADDR=http://localhost:4646
CONSUL_ADDR=http://localhost:8500

# 数据库配置
DB_NAME=final_ddd_dev
DB_USER=postgres
DB_PASSWORD=dev_password

# 应用配置
JWT_SECRET=dev_jwt_secret_key_123456
LOG_LEVEL=debug

# Registry 配置
REGISTRY_NODE=node1
USE_LOCAL_REGISTRY=true
LOCAL_REGISTRY_ADDR=localhost:5000

# 镜像配置
APP_IMAGE=localhost:5000/final-ddd:latest
