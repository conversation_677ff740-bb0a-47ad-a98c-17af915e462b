job "redis" {
  datacenters = ["dc1"]
  type = "service"

  group "redis" {
    count = 1

    # 数据持久化
    host_volume "redis-data" {
      path      = "/opt/data/redis"
      read_only = false
    }

    network {
      port "redis" {
        static = 6379
      }
    }

    service {
      name = "redis"
      port = "redis"

      check {
        name     = "redis-health"
        type     = "tcp"
        port     = "redis"
        interval = "10s"
        timeout  = "3s"
      }
    }

    task "redis" {
      driver = "docker"

      config {
        image = "redis:7-alpine"
        ports = ["redis"]
        force_pull = false

        volumes = [
          "/opt/data/redis:/data"
        ]

        args = [
          "redis-server",
          "--appendonly", "yes",
          "--requirepass", "${REDIS_PASSWORD}"
        ]
      }

      env {
        REDIS_PASSWORD = "${REDIS_PASSWORD}"
      }

      resources {
        cpu    = 200
        memory = 256
      }
    }
  }
}
